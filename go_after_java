GOROOT=/usr/local/opt/go/libexec #gosetup
GOPATH=/Users/<USER>/go #gosetup
/usr/local/opt/go/libexec/bin/go build -o /Users/<USER>/Library/Caches/JetBrains/IntelliJIdea2024.1/tmp/GoLand/___1go_build_github_com_decurret_lab_dcbg_dcf_bcmonitoring_sandbox_cmd_bc_monitoring github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/cmd/bc_monitoring #gosetup
/Users/<USER>/Library/Caches/JetBrains/IntelliJIdea2024.1/tmp/GoLand/___1go_build_github_com_decurret_lab_dcbg_dcf_bcmonitoring_sandbox_cmd_bc_monitoring
time="2025-07-29 16:03:34" level=info msg="starting bc monitoring" func=main.main file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/cmd/bc_monitoring/main.go:23" process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="downloading abi files..." func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.(*DownloadAbiInteractor).Execute" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go:34" bucket_name=abijson-local-bucket process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="getting s3 abi object." func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.(*DownloadAbiInteractor).Execute" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go:61" bucketName=abijson-local-bucket objKey=3000/AccessCtrl.json process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="abi files downloaded." func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.(*DownloadAbiInteractor).parseAbiContent" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go:113" address=0xd06e313b7fd121a0130946210550ad543270883f contract_name=AccessCtrl events="map[Initialized:event Initialized(uint8 version) RoleAdminChanged:event RoleAdminChanged(bytes32 indexed role, bytes32 indexed previousAdminRole, bytes32 indexed newAdminRole) RoleGranted:event RoleGranted(bytes32 indexed role, address indexed account, address indexed sender) RoleRevoked:event RoleRevoked(bytes32 indexed role, address indexed account, address indexed sender)]" last_modified="2025-07-29 09:01:56 +0000 UTC" process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="getting s3 abi object." func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.(*DownloadAbiInteractor).Execute" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go:61" bucketName=abijson-local-bucket objKey=3000/Account.json process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="abi files downloaded." func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.(*DownloadAbiInteractor).parseAbiContent" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go:113" address=0x8f4454438bb3ab002d13652f6fb39b0471c707e7 contract_name=Account events="map[AccountEnabled:event AccountEnabled(bytes32 indexed accountId, bytes32 accountStatus, bytes32 reasonCode, bytes32 traceId) AccountTerminated:event AccountTerminated(bytes32 accountId, bytes32 reasonCode, bytes32 traceId) AddAccountRole:event AddAccountRole(bytes32 indexed accountId, address accountEoa, bytes32 traceId) AddZone:event AddZone(bytes32 indexed accountId, uint16 zoneId, bytes32 traceId) AfterBalance:event AfterBalance((uint16,uint256)[] fromAfterBalance, (uint16,uint256)[] toAfterBalance, bytes32 traceId) ForceBurn:event ForceBurn(bytes32 validatorId, bytes32 accountId, bytes32 traceId, uint256 totalBalance, uint256 burnedAmount, uint256 burnedBalance, (uint16,uint256)[] forceDischarge) Initialized:event Initialized(uint8 version) ModAccount:event ModAccount(bytes32 accountId, string accountName, bytes32 traceId)]" last_modified="2025-07-29 09:01:56 +0000 UTC" process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="getting s3 abi object." func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.(*DownloadAbiInteractor).Execute" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go:61" bucketName=abijson-local-bucket objKey=3000/AccountSyncBridge.json process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="abi files downloaded." func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.(*DownloadAbiInteractor).parseAbiContent" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go:113" address=0xdc64a140aa3e981100a9beca4e685f962f0cf6c9 contract_name=AccountSyncBridge events="map[Initialized:event Initialized(uint8 version)]" last_modified="2025-07-29 09:01:56 +0000 UTC" process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="getting s3 abi object." func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.(*DownloadAbiInteractor).Execute" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go:61" bucketName=abijson-local-bucket objKey=3000/BalanceSyncBridge.json process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="abi files downloaded." func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.(*DownloadAbiInteractor).parseAbiContent" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go:113" address=0x9fe46736679d2d9a65f0992f2272de9f3c7fa6e0 contract_name=BalanceSyncBridge events="map[Initialized:event Initialized(uint8 version)]" last_modified="2025-07-29 09:01:56 +0000 UTC" process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="getting s3 abi object." func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.(*DownloadAbiInteractor).Execute" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go:61" bucketName=abijson-local-bucket objKey=3000/BusinessZoneAccount.json process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="abi files downloaded." func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.(*DownloadAbiInteractor).parseAbiContent" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go:113" address=0x91c4b78aa3013ecf8f9f33f2222e711839c2e1d3 contract_name=BusinessZoneAccount events="map[BalanceUpdateByIssueVoucher:event BalanceUpdateByIssueVoucher(uint16 zoneId, bytes32 accountId, uint256 amount, bytes32 traceId) BalanceUpdateByRedeemVoucher:event BalanceUpdateByRedeemVoucher(uint16 zoneId, bytes32 accountId, uint256 amount, bytes32 traceId) Initialized:event Initialized(uint8 version) SetActiveBusinessAccountWithZone:event SetActiveBusinessAccountWithZone(bytes32 accountId, uint256 zoneId, bytes32 traceId) SyncBusinessZoneBalance:event SyncBusinessZoneBalance((bytes32,uint16,bytes32,bytes32,uint256,uint256,uint256,uint16,bytes32,bytes32,string,bytes32,string,uint256,bytes32,string,string) transferData, bytes32 traceId) SyncBusinessZoneStatus:event SyncBusinessZoneStatus(bytes32 validatorId, bytes32 accountId, uint256 zoneId, string zoneName, bytes32 accountStatus, bytes32 traceId)]" last_modified="2025-07-29 09:01:56 +0000 UTC" process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="getting s3 abi object." func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.(*DownloadAbiInteractor).Execute" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go:61" bucketName=abijson-local-bucket objKey=3000/ContractManager.json process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="abi files downloaded." func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.(*DownloadAbiInteractor).parseAbiContent" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go:113" address=0x7539e0b6aea3da42058b0ed4409265d8f909cc00 contract_name=ContractManager events="map[Initialized:event Initialized(uint8 version)]" last_modified="2025-07-29 09:01:56 +0000 UTC" process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="getting s3 abi object." func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.(*DownloadAbiInteractor).Execute" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go:61" bucketName=abijson-local-bucket objKey=3000/Error.json process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="abi files downloaded." func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.(*DownloadAbiInteractor).parseAbiContent" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go:113" address=0xae694e30623c5e214d87abe7150f966f69c7310c contract_name=Error events="map[]" last_modified="2025-07-29 09:01:56 +0000 UTC" process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="getting s3 abi object." func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.(*DownloadAbiInteractor).Execute" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go:61" bucketName=abijson-local-bucket objKey=3000/FinancialCheck.json process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="abi files downloaded." func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.(*DownloadAbiInteractor).parseAbiContent" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go:113" address=0x29232a2d4daf418fa972050f364d9c94d8bdde9c contract_name=FinancialCheck events="map[Initialized:event Initialized(uint8 version)]" last_modified="2025-07-29 09:01:56 +0000 UTC" process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="getting s3 abi object." func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.(*DownloadAbiInteractor).Execute" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go:61" bucketName=abijson-local-bucket objKey=3000/FinancialZoneAccount.json process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="abi files downloaded." func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.(*DownloadAbiInteractor).parseAbiContent" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go:113" address=0x85af8bdfd640c42429e620fdd891c46b1c4436a6 contract_name=FinancialZoneAccount events="map[AddAccountLimit:event AddAccountLimit(bytes32 accountId, (uint256,uint256,uint256,uint256,uint256,(uint256,uint256,uint256,uint256,uint256,uint256)) limitValues, bytes32 traceId) AddCumulativeAmount:event AddCumulativeAmount(bytes32 accountId, uint256 amount, uint256 cumulativeDate, uint256 cumulativeAmount, bytes32 traceId) Initialized:event Initialized(uint8 version) SubtractCumulativeAmount:event SubtractCumulativeAmount(bytes32 accountId, uint256 amount, uint256 cumulativeDate, uint256 cumulativeAmount, bytes32 traceId) SyncBurn:event SyncBurn(bytes32 accountId, uint256 amount, uint256 cumulativeDate, uint256 cumulativeAmount, uint256 cumulativeBurnAmount, bytes32 traceId) SyncCharge:event SyncCharge(bytes32 accountId, uint256 amount, uint256 cumulativeDate, uint256 cumulativeAmount, uint256 cumulativeChargeAmount, bytes32 traceId) SyncCumulativeReset:event SyncCumulativeReset(bytes32 accountId, bytes32 traceId) SyncDischarge:event SyncDischarge(bytes32 accountId, uint256 amount, uint256 cumulativeDate, uint256 cumulativeAmount, uint256 cumulativeDischargeAmount, bytes32 traceId) SyncMint:event SyncMint(bytes32 accountId, uint256 amount, uint256 cumulativeDate, uint256 cumulativeAmount, uint256 cumulativeMintAmount, bytes32 traceId) SyncTransfer:event SyncTransfer(bytes32 accountId, uint256 amount, uint256 cumulativeDate, uint256 cumulativeAmount, uint256 cumulativeTransferAmount, bytes32 traceId)]" last_modified="2025-07-29 09:01:56 +0000 UTC" process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="getting s3 abi object." func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.(*DownloadAbiInteractor).Execute" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go:61" bucketName=abijson-local-bucket objKey=3000/IBCToken.json process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="abi files downloaded." func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.(*DownloadAbiInteractor).parseAbiContent" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go:113" address=0x4e593f9fabba7bc6ff3b2b5cadb3416f967e1cbd contract_name=IBCToken events="map[DischargeRequested:event DischargeRequested(bytes32 indexed validatorId, bytes32 accountId, uint16 fromZoneId, uint256 amount, bytes32 traceId) Initialized:event Initialized(uint8 version) IssueVoucher:event IssueVoucher(uint16 indexed zoneId, bytes32 indexed validatorId, bytes32 accountId, string accountName, uint256 amount, uint256 balance, bytes32 traceId) RedeemVoucher:event RedeemVoucher(uint16 indexed zoneId, bytes32 indexed validatorId, bytes32 accountId, string accountName, uint256 amount, uint256 balance, bytes32 traceId) Transfer:event Transfer((bytes32,uint16,bytes32,bytes32,uint256,uint256,uint256,uint16,bytes32,bytes32,string,bytes32,string,uint256,bytes32,string,string) transferData, bytes32 traceId)]" last_modified="2025-07-29 09:01:56 +0000 UTC" process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="getting s3 abi object." func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.(*DownloadAbiInteractor).Execute" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go:61" bucketName=abijson-local-bucket objKey=3000/Issuer.json process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="abi files downloaded." func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.(*DownloadAbiInteractor).parseAbiContent" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go:113" address=0x65ca593a447a5e3f4e1228cda776d360841eaf5a contract_name=Issuer events="map[AddAccountByIssuer:event AddAccountByIssuer(bytes32 indexed issuerId, bytes32 accountId, bytes32 traceId) AddBizZoneToIssuer:event AddBizZoneToIssuer(bytes32 issuerId, uint16 zoneId, bytes32 traceId) AddIssuer:event AddIssuer(bytes32 indexed issuerId, uint16 indexed bankCode, string name, bytes32 traceId) AddIssuerRole:event AddIssuerRole(bytes32 indexed issuerId, address issuerEoa, bytes32 traceId) CumulativeReset:event CumulativeReset(bytes32 issuerId, bytes32 accountId, bytes32 traceId, bytes32 validatorId) DeleteBizZoneToIssuer:event DeleteBizZoneToIssuer(bytes32 issuerId, uint16 zoneId, bytes32 traceId) Initialized:event Initialized(uint8 version) IssuerEnabled:event IssuerEnabled(bytes32 indexed issuerId, bytes32 traceId) ModIssuer:event ModIssuer(bytes32 indexed issuerId, string name, bytes32 traceId) ModTokenLimit:event ModTokenLimit(bytes32 indexed validatorId, bytes32 indexed accountId, (bool,bool,bool,bool,bool,(bool,bool,bool,bool,bool,bool)) limitUpdates, (uint256,uint256,uint256,uint256,uint256,(uint256,uint256,uint256,uint256,uint256,uint256)) limitValues, bytes32 traceId)]" last_modified="2025-07-29 09:01:56 +0000 UTC" process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="getting s3 abi object." func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.(*DownloadAbiInteractor).Execute" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go:61" bucketName=abijson-local-bucket objKey=3000/JPYTokenTransferBridge.json process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="abi files downloaded." func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.(*DownloadAbiInteractor).parseAbiContent" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go:113" address=0x5fbdb2315678afecb367f032d93f642f64180aa3 contract_name=JPYTokenTransferBridge events="map[Initialized:event Initialized(uint8 version)]" last_modified="2025-07-29 09:01:56 +0000 UTC" process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="getting s3 abi object." func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.(*DownloadAbiInteractor).Execute" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go:61" bucketName=abijson-local-bucket objKey=3000/OwnableIBCHandler.json process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="abi files downloaded." func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.(*DownloadAbiInteractor).parseAbiContent" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go:113" address=0xa91c0e770367c97d6c515bbfc96e60383ae17aec contract_name=OwnableIBCHandler events="map[AcknowledgePacket:event AcknowledgePacket((uint64,string,string,string,string,bytes,(uint64,uint64),uint64) packet, bytes acknowledgement) GeneratedChannelIdentifier:event GeneratedChannelIdentifier(string arg0) GeneratedClientIdentifier:event GeneratedClientIdentifier(string arg0) GeneratedConnectionIdentifier:event GeneratedConnectionIdentifier(string arg0) OwnershipTransferred:event OwnershipTransferred(address indexed previousOwner, address indexed newOwner) RecvPacket:event RecvPacket((uint64,string,string,string,string,bytes,(uint64,uint64),uint64) packet) SendPacket:event SendPacket(uint64 sequence, string sourcePort, string sourceChannel, (uint64,uint64) timeoutHeight, uint64 timeoutTimestamp, bytes data) TimeoutPacket:event TimeoutPacket((uint64,string,string,string,string,bytes,(uint64,uint64),uint64) packet) WriteAcknowledgement:event WriteAcknowledgement(string destinationPortId, string destinationChannel, uint64 sequence, bytes acknowledgement)]" last_modified="2025-07-29 09:01:56 +0000 UTC" process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="getting s3 abi object." func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.(*DownloadAbiInteractor).Execute" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go:61" bucketName=abijson-local-bucket objKey=3000/Provider.json process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="abi files downloaded." func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.(*DownloadAbiInteractor).parseAbiContent" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go:113" address=0x88d107c2f5cc64d09634debce707033bbe8287e4 contract_name=Provider events="map[AddBizZone:event AddBizZone(uint16 zoneId, string zoneName) AddProvider:event AddProvider(bytes32 indexed providerId, uint16 zoneId, string zoneName, bytes32 traceId) AddProviderRole:event AddProviderRole(bytes32 indexed providerId, address providerEoa, bytes32 traceId) AddTokenByProvider:event AddTokenByProvider(bytes32 indexed providerId, bytes32 tokenId, bytes32 traceId) Initialized:event Initialized(uint8 version) ModProvider:event ModProvider(bytes32 indexed providerId, bytes32 name, bytes32 traceId) ModZone:event ModZone(bytes32 providerId, string zoneName, bytes32 traceId) ProviderEnabled:event ProviderEnabled(bytes32 indexed providerId, bool enabled, bytes32 traceId) SetTokenIdByProvider:event SetTokenIdByProvider(bytes32 indexed providerId, bytes32 tokenId, bytes32 traceId)]" last_modified="2025-07-29 09:01:56 +0000 UTC" process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="getting s3 abi object." func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.(*DownloadAbiInteractor).Execute" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go:61" bucketName=abijson-local-bucket objKey=3000/RemigrationBackup.json process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="abi files downloaded." func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.(*DownloadAbiInteractor).parseAbiContent" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go:113" address=0xf1a840c704c572f3bb9c59296c8fdf9a4ad30b89 contract_name=RemigrationBackup events="map[Initialized:event Initialized(uint8 version)]" last_modified="2025-07-29 09:01:56 +0000 UTC" process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="getting s3 abi object." func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.(*DownloadAbiInteractor).Execute" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go:61" bucketName=abijson-local-bucket objKey=3000/RemigrationRestore.json process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="abi files downloaded." func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.(*DownloadAbiInteractor).parseAbiContent" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go:113" address=0x991478d85422a8a31c3c64f91d993e75253c2a40 contract_name=RemigrationRestore events="map[Initialized:event Initialized(uint8 version)]" last_modified="2025-07-29 09:01:56 +0000 UTC" process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="getting s3 abi object." func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.(*DownloadAbiInteractor).Execute" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go:61" bucketName=abijson-local-bucket objKey=3000/Token.json process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="abi files downloaded." func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.(*DownloadAbiInteractor).parseAbiContent" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go:113" address=0x25b231e9a52141c72a9d549378fabf12d730bbaf contract_name=Token events="map[AddToken:event AddToken(bytes32 indexed tokenId, uint16 zoneId, string zoneName, bool enabled, bytes32 traceId) Approval:event Approval(bytes32 validatorId, bytes32 indexed ownerId, bytes32 spenderId, uint256 amount, bytes32 traceId) Burn:event Burn(uint16 zoneId, bytes32 validatorId, bytes32 indexed issuerId, bytes32 accountId, string accountName, uint256 amount, uint256 balance, bytes32 traceId) BurnCancel:event BurnCancel(uint16 zoneId, bytes32 validatorId, bytes32 indexed issuerId, bytes32 accountId, string accountName, uint256 amount, uint256 balance, uint256 blockTimestamp, bytes32 traceId) CustomTransfer:event CustomTransfer(bytes32 sendAccountId, bytes32 fromAccountId, bytes32 toAccountId, uint256 amount, bytes32 miscValue1, string miscValue2, bytes32 traceId) Initialized:event Initialized(uint8 version) Mint:event Mint(uint16 zoneId, bytes32 validatorId, bytes32 indexed issuerId, bytes32 accountId, string accountName, uint256 amount, uint256 balance, bytes32 traceId) ModToken:event ModToken(bytes32 indexed tokenId, bytes32 name, bytes32 symbol, bytes32 traceId) SetEnabledToken:event SetEnabledToken(bytes32 indexed tokenId, bool enabled, bytes32 traceId) Transfer:event Transfer((bytes32,uint16,bytes32,bytes32,uint256,uint256,uint256,uint16,bytes32,bytes32,string,bytes32,string,uint256,bytes32,string,string) transferData, bytes32 traceId)]" last_modified="2025-07-29 09:01:56 +0000 UTC" process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="getting s3 abi object." func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.(*DownloadAbiInteractor).Execute" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go:61" bucketName=abijson-local-bucket objKey=3000/TransferProxy.json process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="abi files downloaded." func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.(*DownloadAbiInteractor).parseAbiContent" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go:113" address=0x9d95413473d1d5df59caa2053a1c4d544a6e1c1d contract_name=TransferProxy events="map[AddRule:event AddRule(address rule, uint256 position) DeleteRule:event DeleteRule(address rule) Initialized:event Initialized(uint8 version)]" last_modified="2025-07-29 09:01:56 +0000 UTC" process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="getting s3 abi object." func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.(*DownloadAbiInteractor).Execute" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go:61" bucketName=abijson-local-bucket objKey=3000/Validator.json process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="abi files downloaded." func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.(*DownloadAbiInteractor).parseAbiContent" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/download_abi.go:113" address=0x2b696cea919a5d8b6c823a81341fdb4be1ad8ff9 contract_name=Validator events="map[AddAccount:event AddAccount(bytes32 indexed validatorId, bytes32 accountId, bool identified, bool enabled, (uint256,uint256,uint256,uint256,uint256,(uint256,uint256,uint256,uint256,uint256,uint256)) limitValues, bytes32 traceId) AddValidator:event AddValidator(bytes32 indexed validatorId, bytes32 issuerId, bytes32 name, bytes32 traceId) AddValidatorAccountId:event AddValidatorAccountId(bytes32 validatorId, bytes32 accountId, bytes32 traceId) AddValidatorRole:event AddValidatorRole(bytes32 indexed validatorId, address validatorEoa, bytes32 traceId) Initialized:event Initialized(uint8 version) ModValidator:event ModValidator(bytes32 indexed validatorId, bytes32 name, bytes32 traceId) SetBizZoneTerminated:event SetBizZoneTerminated(bytes32 validatorId, uint16 indexed zoneId, bytes32 accountId, bytes32 traceId) SetTerminated:event SetTerminated(uint16 indexed zoneId, bytes32 accountId, bytes32 reasonCode, bytes32 traceId) SyncAccount:event SyncAccount(bytes32 validatorId, bytes32 accountId, string accountName, uint16 zoneId, string zoneName, bytes32 accountStatus, uint256 approvalAmount, bytes32 traceId) ValidatorEnabled:event ValidatorEnabled(bytes32 indexed validatorId, bool enabled, bytes32 traceId)]" last_modified="2025-07-29 09:01:56 +0000 UTC" process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="started bc monitoring" func=main.main file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/cmd/bc_monitoring/main.go:33" process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="get blockheight: 0x0" func=github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.MonitorEventInteractor.Execute file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/monitor_event.go:47" process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="blockHeight: 1" func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/infrastructure/ethereum.(*EthEventLogDao).GetPendingTransactions" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/infrastructure/ethereum/logs.go:203" process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="start subscribe event" func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/infrastructure/ethereum.(*EthEventLogDao).SubscribeAll.func1" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/infrastructure/ethereum/logs.go:57" process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="event found" func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/infrastructure/ethereum.(*EthEventLogDao).GetPendingTransactions.func1" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/infrastructure/ethereum/logs.go:221" process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c tx_hash=0x374436d7ca8bf4134c3df8b88b4828c6608e7cb79774a36d80964c2d0969f25d
time="2025-07-29 16:03:34" level=info msg="event parsed" func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/infrastructure/ethereum.(*EthEventLogDao).GetPendingTransactions.func1" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/infrastructure/ethereum/logs.go:229" name=Initialized process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c tx_hash=0x374436d7ca8bf4134c3df8b88b4828c6608e7cb79774a36d80964c2d0969f25d
time="2025-07-29 16:03:34" level=info msg="event found" func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/infrastructure/ethereum.(*EthEventLogDao).GetPendingTransactions.func1" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/infrastructure/ethereum/logs.go:221" process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c tx_hash=0x0f6acb4cc4287e198e801050654c4f0a3ee70cb71778a2e7571e7469c57f048f
time="2025-07-29 16:03:34" level=info msg="pending block height Number is: 0xd4" func=github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.MonitorEventInteractor.Execute file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/monitor_event.go:59" process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="exBlockHeight is: 0x0" func=github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.MonitorEventInteractor.Execute file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/monitor_event.go:60" process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="event parsed" func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/infrastructure/ethereum.(*EthEventLogDao).GetPendingTransactions.func1" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/infrastructure/ethereum/logs.go:229" name=Initialized process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c tx_hash=0x0f6acb4cc4287e198e801050654c4f0a3ee70cb71778a2e7571e7469c57f048f
time="2025-07-29 16:03:34" level=info msg="success to register event" func=github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.MonitorEventInteractor.savePendingTransaction file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/monitor_event.go:161" block_height="{0 212}" block_timestamp=1753778840 event_name=Initialized log_index=0 process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c trace_id= tx_hash=0x374436d7ca8bf4134c3df8b88b4828c6608e7cb79774a36d80964c2d0969f25d
time="2025-07-29 16:03:34" level=info msg="exBlockHeight updated to: entity.BlockHeight{ID:0x0, BlockNumber:0xd4}" func=github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.MonitorEventInteractor.Execute file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/monitor_event.go:83" process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="pending block height Number is: 0xd6" func=github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.MonitorEventInteractor.Execute file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/monitor_event.go:59" process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="exBlockHeight is: 0xd4" func=github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.MonitorEventInteractor.Execute file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/monitor_event.go:60" process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="event found" func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/infrastructure/ethereum.(*EthEventLogDao).GetPendingTransactions.func1" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/infrastructure/ethereum/logs.go:221" process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c tx_hash=0x20a7daed67f050872e586b6fafed4cd7088c7b410c3d86098e81d4295acb0133
time="2025-07-29 16:03:34" level=info msg="event parsed" func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/infrastructure/ethereum.(*EthEventLogDao).GetPendingTransactions.func1" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/infrastructure/ethereum/logs.go:229" name=Initialized process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c tx_hash=0x20a7daed67f050872e586b6fafed4cd7088c7b410c3d86098e81d4295acb0133
time="2025-07-29 16:03:34" level=info msg="success to register block number" func=github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.MonitorEventInteractor.savePendingTransactionBlockNumber file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/monitor_event.go:171" block number=212 process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="success to register event" func=github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.MonitorEventInteractor.savePendingTransaction file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/monitor_event.go:161" block_height="{0 214}" block_timestamp=1753778844 event_name=Initialized log_index=0 process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c trace_id= tx_hash=0x0f6acb4cc4287e198e801050654c4f0a3ee70cb71778a2e7571e7469c57f048f
time="2025-07-29 16:03:34" level=info msg="exBlockHeight updated to: entity.BlockHeight{ID:0x0, BlockNumber:0xd6}" func=github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.MonitorEventInteractor.Execute file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/monitor_event.go:83" process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="pending block height Number is: 0xd8" func=github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.MonitorEventInteractor.Execute file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/monitor_event.go:59" process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="exBlockHeight is: 0xd6" func=github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.MonitorEventInteractor.Execute file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/monitor_event.go:60" process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="pending transactions is done" func="github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/infrastructure/ethereum.(*EthEventLogDao).GetPendingTransactions.func1" file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/infrastructure/ethereum/logs.go:237" process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="success to register block number" func=github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.MonitorEventInteractor.savePendingTransactionBlockNumber file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/monitor_event.go:171" block number=214 process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="success to register event" func=github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.MonitorEventInteractor.savePendingTransaction file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/monitor_event.go:161" block_height="{0 216}" block_timestamp=1753778848 event_name=Initialized log_index=0 process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c trace_id= tx_hash=0x20a7daed67f050872e586b6fafed4cd7088c7b410c3d86098e81d4295acb0133
time="2025-07-29 16:03:34" level=info msg="exBlockHeight updated to: entity.BlockHeight{ID:0x0, BlockNumber:0xd8}" func=github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.MonitorEventInteractor.Execute file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/monitor_event.go:83" process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="pending block height Number is: 0x0" func=github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.MonitorEventInteractor.Execute file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/monitor_event.go:59" process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="exBlockHeight is: 0xd8" func=github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.MonitorEventInteractor.Execute file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/monitor_event.go:60" process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="success to register block number" func=github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.MonitorEventInteractor.savePendingTransactionBlockNumber file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/monitor_event.go:171" block number=216 process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c
time="2025-07-29 16:03:34" level=info msg="pending transactions channel was closed" func=github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase.MonitorEventInteractor.Execute file="/Users/<USER>/BAP/DCP/github-source/dcbg-dcjpy-bcmonitoring/internal/usecase/monitor_event.go:69" process_uuid=adbd4ef7-1664-4e33-99a6-bd719554938c


