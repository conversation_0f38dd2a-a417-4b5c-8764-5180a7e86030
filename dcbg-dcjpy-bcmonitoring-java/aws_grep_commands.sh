#!/bin/bash

# AWS API Search Commands - Dựa trên kết quả phân tích thực tế
# Chỉ những command cần thiết để tìm AWS API đang được sử dụng

echo "=== Essential AWS API Search Commands ==="

# 1. Tì<PERSON> tất cả AWS Request objects (pattern chính)
echo "1. All AWS Request objects:"
grep -Er "\.model\..*Request" src/main/java

# 2. Tìm direct method calls trên AWS clients
echo -e "\n2. Direct AWS client method calls:"
grep -Er "(s3Client|client)\.(listObjectsV2|getObject|putItem|query|listBuckets)" src/main/java

# 3. Tìm AWS imports để biết services nào được dùng
echo -e "\n3. AWS service imports:"
grep -Er "import.*awssdk\.services\." src/main/java

# 4. Tìm tất cả AWS API calls trong một command
echo -e "\n4. All AWS API calls (comprehensive):"
grep -Er "\.(listObjectsV2|getObject|putObject|putItem|query|scan|listBuckets|sendMessage)" src/main/java
