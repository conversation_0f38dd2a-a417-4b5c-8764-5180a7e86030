#!/bin/bash

# AWS SDK API Search Commands for Java Repository
# Excludes test files (.groovy and test directories)

echo "=== Searching for AWS SDK API calls in main source code only ==="

# 1. Search for all AWS SDK method calls (most comprehensive)
echo "1. All AWS SDK method calls:"
find src/main -name "*.java" | xargs grep -n "\\.listObjectsV2\\|\\.getObject\\|\\.putObject\\|\\.deleteObject\\|\\.putItem\\|\\.getItem\\|\\.updateItem\\|\\.deleteItem\\|\\.scan\\|\\.query\\|\\.batchGetItem\\|\\.batchWriteItem\\|\\.sendMessage\\|\\.receiveMessage\\|\\.deleteMessage\\|\\.purgeQueue\\|\\.createQueue\\|\\.deleteQueue\\|\\.listBuckets\\|\\.createBucket\\|\\.deleteBucket"

echo -e "\n2. S3 specific operations:"
find src/main -name "*.java" | xargs grep -n "s3Client\\." | grep -v "build\\|builder"

echo -e "\n3. DynamoDB specific operations:"
find src/main -name "*.java" | xargs grep -n "client\\.putItem\\|client\\.query\\|client\\.scan\\|client\\.getItem\\|client\\.updateItem\\|client\\.deleteItem"

echo -e "\n4. SQS specific operations:"
find src/main -name "*.java" | xargs grep -n "sqsClient\\." | grep -v "build\\|builder"

echo -e "\n5. AWS SDK imports (to identify services):"
find src/main -name "*.java" | xargs grep -n "import.*amazon.*awssdk\\.services" | grep -v "auth\\|regions\\|core"

echo -e "\n6. Search for AWS client variable usage:"
find src/main -name "*.java" | xargs grep -n "\\(s3Client\\|dynamoDbClient\\|sqsClient\\)\\." | grep -v "build\\|builder\\|create"

echo -e "\n7. Search for AWS request/response objects:"
find src/main -name "*.java" | xargs grep -n "Request\\|Response" | grep -i "s3\\|dynamo\\|sqs"

echo -e "\n=== Quick search for specific AWS operations ==="

echo -e "\n8. S3 operations only:"
find src/main -name "*.java" | xargs grep -n "\\.listObjectsV2\\|\\.getObject\\|\\.putObject\\|\\.deleteObject\\|\\.listBuckets\\|\\.createBucket\\|\\.deleteBucket"

echo -e "\n9. DynamoDB operations only:"
find src/main -name "*.java" | xargs grep -n "\\.putItem\\|\\.getItem\\|\\.updateItem\\|\\.deleteItem\\|\\.scan\\|\\.query\\|\\.batchGetItem\\|\\.batchWriteItem"

echo -e "\n10. SQS operations only:"
find src/main -name "*.java" | xargs grep -n "\\.sendMessage\\|\\.receiveMessage\\|\\.deleteMessage\\|\\.purgeQueue\\|\\.createQueue\\|\\.deleteQueue"
